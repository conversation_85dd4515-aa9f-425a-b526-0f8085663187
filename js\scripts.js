// 定義存 DB 的 key
const STORAGE_KEYS = {
    THEME: 'lottery_theme',
    PRIZES: 'lottery_prizes',
    PARTICIPANTS: 'lottery_participants',
    SETTINGS: 'lottery_settings'
};
window.currentSettings = {
    drawMode: 'all',
    winRule: 'multiple',
    skipAnimation: false
};

// 修改全域變數追蹤抽獎狀態
let drawingState = {
    isDrawing: false,
    currentPrizeIndex: 0,
    currentCount: 0,  // 新增：追蹤當前獎項已抽出的數量
    remainingParticipants: []
};

let participantsUndoStack = [];

// 修改控制按鈕和輸入框狀態的函數
function setControlButtonsState(enabled) {
    // 控制一般按鈕狀態（排除抽獎按鈕和錄影按鈕）
    const controlButtons = document.querySelectorAll('button:not(.js-lottery):not(#recordButton)');
    controlButtons.forEach(button => button.disabled = !enabled);
    
    // 控制輸入框狀態
    const inputs = document.querySelectorAll('input, textarea');
    inputs.forEach(input => input.disabled = !enabled);
}

// 修改抽獎按鈕狀態的函數
function setDrawButtonState(enabled) {
    const submitButton = document.querySelector('.js-lottery');
    if (submitButton) {
        submitButton.disabled = !enabled;
    }
}

// ====== IndexedDB 工具函數 ======
const DB_NAME = 'lottery_' + window.location.pathname;
const DB_VERSION = 1;
const STORE_NAME = 'kv';

function openDB() {
    return new Promise((resolve, reject) => {
        const req = indexedDB.open(DB_NAME, DB_VERSION);
        req.onupgradeneeded = function(e) {
            const db = e.target.result;
            if (!db.objectStoreNames.contains(STORE_NAME)) {
                db.createObjectStore(STORE_NAME);
            }
        };
        req.onsuccess = function(e) {
            resolve(e.target.result);
        };
        req.onerror = function(e) {
            reject(e);
        };
    });
}

async function idbSetItem(key, value) {
    const db = await openDB();
    return new Promise((resolve, reject) => {
        const tx = db.transaction(STORE_NAME, 'readwrite');
        const store = tx.objectStore(STORE_NAME);
        store.put(value, key);
        tx.oncomplete = resolve;
        tx.onerror = reject;
    });
}

async function idbGetItem(key) {
    const db = await openDB();
    return new Promise((resolve, reject) => {
        const tx = db.transaction(STORE_NAME, 'readonly');
        const store = tx.objectStore(STORE_NAME);
        const req = store.get(key);
        req.onsuccess = () => resolve(req.result);
        req.onerror = reject;
    });
}

async function idbRemoveItem(key) {
    const db = await openDB();
    return new Promise((resolve, reject) => {
        const tx = db.transaction(STORE_NAME, 'readwrite');
        const store = tx.objectStore(STORE_NAME);
        store.delete(key);
        tx.oncomplete = resolve;
        tx.onerror = reject;
    });
}

async function idbGetAllKeys() {
    const db = await openDB();
    return new Promise((resolve, reject) => {
        const tx = db.transaction(STORE_NAME, 'readonly');
        const store = tx.objectStore(STORE_NAME);
        const req = store.getAllKeys();
        req.onsuccess = () => resolve(req.result);
        req.onerror = reject;
    });
}
// ====== End IndexedDB 工具 ======

// 共用：取得所有有效獎項陣列（格式：['獎品名稱,數量', ...]）
function getPrizeList() {
    const prizeRows = document.querySelectorAll('#awards-block .awards__row');
    return Array.from(prizeRows).map(row => {
        const nameInput = row.querySelector('input[type="text"]');
        const countInput = row.querySelector('input[type="number"]');
        const name = nameInput ? nameInput.value.trim() : '';
        let count = 1;
        if (countInput) {
            const val = countInput.value.trim();
            count = val && !isNaN(val) ? parseInt(val) : 1;
        }
        return name ? `${name},${count}` : '';
    }).filter(str => str);
}

// 修改 saveToStorage
async function saveToStorage(key, value) {
    await idbSetItem(key, value);
}

// 修改 loadStoredData
async function loadStoredData() {
    // 載入獎項內容
    const storedPrizes = await idbGetItem(STORAGE_KEYS.PRIZES);
    if (storedPrizes) {
        try {
            const prizesData = JSON.parse(storedPrizes);
            if (Array.isArray(prizesData)) {
                // 更新表格模式
                updateAwardsTable(prizesData);
                // 更新 textarea 模式（只更新非空的行）
                const awardsTextarea = document.getElementById('awards-textarea');
                if (awardsTextarea) {
                    const lines = prizesData
                        .filter(prize => prize.name && prize.name.trim()) // 只包含有名稱的獎項
                        .map(prize => `${prize.name},${prize.count}`)
                        .join('\n');
                    awardsTextarea.value = lines;
                }
            }
        } catch (e) {
            console.error('載入獎項資料時發生錯誤:', e);
        }
    }
    
    // 載入參與者
    const storedParticipants = await idbGetItem(STORAGE_KEYS.PARTICIPANTS);
    if (storedParticipants) {
        document.getElementById('participants').value = storedParticipants;
        updateParticipantsCount(); // 載入後更新計數
    }
    
    // 載入設定
    const storedSettings = await idbGetItem(STORAGE_KEYS.SETTINGS);
    if (storedSettings) {
        window.currentSettings = JSON.parse(storedSettings);
        
        // 設定抽獎方式
        if (window.currentSettings.drawMode === 'sequential') {
            $('#radio-pop-1-2').prop('checked', true);
        } else {
            $('#radio-pop-1-1').prop('checked', true);
        }
        
        // 設定重複中獎規則
        if (window.currentSettings.winRule === 'single') {
            $('#radio-pop-2-2').prop('checked', true);
        } else {
            $('#radio-pop-2-1').prop('checked', true);
        }
        
        // 設定動畫開關
        if (window.currentSettings.skipAnimation === false) {
            $('#radio-pop-3-1').prop('checked', true);
        } else {
            $('#radio-pop-3-2').prop('checked', true);
        }
    }
}

// 修改 setupStorageListeners
function setupStorageListeners() {
    // 獎項輸入框監聽（使用事件委派）
    const awardsBlock = document.getElementById('awards-block');
    if (awardsBlock) {
        awardsBlock.addEventListener('input', function(e) {
            if (e.target.matches('input[type="text"], input[type="number"]')) {
                // 延遲儲存，避免頻繁更新
                clearTimeout(window.awardsSaveTimeout);
                window.awardsSaveTimeout = setTimeout(() => {
                    saveAwardsToStorage();
                }, 500);
            }
        });
    }
    
    // textarea 模式監聽
    const awardsTextarea = document.getElementById('awards-textarea');
    if (awardsTextarea) {
        awardsTextarea.addEventListener('input', function() {
            // 延遲儲存，避免頻繁更新
            clearTimeout(window.awardsTextareaSaveTimeout);
            window.awardsTextareaSaveTimeout = setTimeout(() => {
                saveAwardsToStorage();
            }, 500);
        });
    }
    
    // 參與者輸入框
    const participantsInput = $('#participants')[0];
    participantsInput.addEventListener('input', function(e) {
        saveToStorage(STORAGE_KEYS.PARTICIPANTS, e.target.value);
        updateParticipantsCount(); // 更新參與者數量顯示
    });
}

// 新增：儲存獎項內容到 IndexedDB
async function saveAwardsToStorage() {
    const awardsBlock = document.getElementById('awards-block');
    const awardsTextarea = document.getElementById('awards-textarea');
    
    if (!awardsBlock || !awardsTextarea) return;
    
    // 檢查哪個模式是 active
    const isTextareaMode = awardsTextarea.closest('.awards__wrap').classList.contains('active');
    
    let prizesData = [];
    
    if (isTextareaMode) {
        // 從 textarea 取得資料
        const lines = awardsTextarea.value.trim().split('\n').filter(line => line.trim());
        lines.forEach(line => {
            const parts = line.split(',');
            const name = parts[0]?.trim() || '';
            const count = parts[1]?.trim() || '1';
            if (name) {
                prizesData.push({ name, count });
            }
        });
    } else {
        // 從表格取得資料（包括空的獎項行）
        const rows = awardsBlock.querySelectorAll('.awards__row');
        rows.forEach(row => {
            const nameInput = row.querySelector('input[type="text"]');
            const countInput = row.querySelector('input[type="number"]');
            
            if (nameInput && countInput) {
                const name = nameInput.value.trim();
                const count = countInput.value.trim();
                
                // 儲存所有行，包括空的（這樣新增的空行也會被保留）
                prizesData.push({ name, count });
            }
        });
    }
    
    // 儲存到 IndexedDB
    await saveToStorage(STORAGE_KEYS.PRIZES, JSON.stringify(prizesData));
}

// 滾動到中獎結果區
function scrollToResult() {
    var $result = $('section.result');
    if ($result.length > 0) {
        $result.addClass('active').show().fadeIn(100, function () {
            $('html, body').animate({
                scrollTop: $result.offset().top - 90
            }, 800);
        });
    }
}

// 修改 performDraw
async function performDraw() {
    if (window.currentSettings.drawMode === 'sequential' && drawingState.isDrawing) {
        setControlButtonsState(false);
        drawNextPrize(drawingState.remainingParticipants);
        scrollToResult(); // 新增：每次繼續都滾動
        return;
    }
    
    await idbRemoveItem('lotteryWinners');
    
    const participants = $('#participants')[0].value.trim().split('\n');

    // 清除之前的中獎結果顯示
    const resultSection = document.querySelector('.result');
    if (resultSection) {
        const formBody = resultSection.querySelector('.form__body');
        if (formBody) {
            formBody.innerHTML = '';
        }
        // 確保結果區塊顯示
        resultSection.style.display = 'block';
    }
    scrollToResult(); // 新增：第一次抽獎也滾動
    
    var $result = $('section.result');
    if ($result.length > 0) {
        $result.addClass('active').show().fadeIn(100, function () {
            $('html, body').animate({
                scrollTop: $result.offset().top - 90
            }, 800);
        });
    }
    
    drawingState = {
        isDrawing: true,
        currentPrizeIndex: 0,
        currentCount: 0,
        remainingParticipants: [...participants]
    };
    updateDrawButton();
    drawNextPrize(drawingState.remainingParticipants);
}

function drawNextPrize(participants) {
    scrollToResult(); // 新增：每次進入新獎項都滾動
    // 取得所有獎項列
    const prizes = getPrizeList();
    
    // 取得結果區塊的表單主體作為中獎結果容器
    const resultSection = document.querySelector('.result');
    if (!resultSection) {
        console.error('找不到結果區塊');
        return;
    }
    const formBody = resultSection.querySelector('.form__body');
    if (!formBody) {
        console.error('找不到表單主體');
        return;
    }
    
    // 在開始新的抽獎時禁用所有控制項
    setControlButtonsState(false);
    setDrawButtonState(false);
    
    if (drawingState.currentPrizeIndex >= prizes.length) {
        celebrateCompletion();
        drawingState.isDrawing = false;
        setControlButtonsState(true);  // 抽獎結束時重新啟用所有控制項
        setDrawButtonState(true);      // 重新啟用抽獎按鈕
        updateDrawButton();
        return;
    }
    
    const prize = prizes[drawingState.currentPrizeIndex];
    let prizeName, prizeCount;
    
    const lastCommaIndex = prize.lastIndexOf(',');
    if (lastCommaIndex !== -1) {
        prizeName = prize.substring(0, lastCommaIndex).trim();
        prizeCount = parseInt(prize.substring(lastCommaIndex + 1).trim()) || 1;
    } else {
        prizeName = prize.trim();
        prizeCount = 1;
    }

    // 建立獎項群組（如果還不存在）
    let prizeGroup = formBody.querySelector(`[data-prize="${prizeName}"]`);
    if (!prizeGroup) {
        prizeGroup = document.createElement('div');
        prizeGroup.className = 'result__wrap';
        prizeGroup.setAttribute('data-prize', prizeName);
        
        // 建立獎項標題
        const prizeTitle = document.createElement('div');
        prizeTitle.className = 'result__title';
        prizeTitle.textContent = `${prizeName} ${prizeCount}名`;
        
        // 建立表格遮罩結構
        const tableMask = document.createElement('div');
        tableMask.className = 'table__mask';
        
        // 建立遮罩區塊
        const maskBlock = document.createElement('div');
        maskBlock.className = 'table__mask-block';
        for (let i = 0; i < 4; i++) {
            const maskItem = document.createElement('div');
            maskItem.className = 'table__mask-item';
            maskBlock.appendChild(maskItem);
        }
        
        // 建立結果區塊
        const resultBlock = document.createElement('div');
        resultBlock.className = 'result__block';
        resultBlock.setAttribute('data-simplebar', '');
        
        const simplebarBlock = document.createElement('div');
        simplebarBlock.className = 'simplebar__block';
        
        // 建立表格
        const table = document.createElement('table');

        // 建立表頭
        const thead = document.createElement('thead');
        const headerRow = document.createElement('tr');
        
        // 根據 excelHeader 建立表頭
        if (window.excelHeader && Array.isArray(window.excelHeader)) {
            window.excelHeader.forEach(header => {
                const th = document.createElement('th');
                th.textContent = header;
                headerRow.appendChild(th);
            });
        } else {
            // 預設表頭
            const defaultHeaders = ['序號', '中獎者'];
            defaultHeaders.forEach(header => {
                const th = document.createElement('th');
                th.textContent = header;
                headerRow.appendChild(th);
            });
        }
        
        thead.appendChild(headerRow);
        table.appendChild(thead);

        // 根據欄位數量設定表格CSS類別
        const columnCount = headerRow.children.length;
        if (columnCount === 2) {
            table.classList.add('cols-2');
        } else if (columnCount === 3) {
            table.classList.add('cols-3');
        } else if (columnCount === 4) {
            table.classList.add('cols-4');
        } else if (columnCount === 5) {
            table.classList.add('cols-5');
        } else if (columnCount >= 6) {
            table.classList.add('cols-many');
        }

        // 建立表身
        const tbody = document.createElement('tbody');
        table.appendChild(tbody);
        
        simplebarBlock.appendChild(table);
        resultBlock.appendChild(simplebarBlock);
        tableMask.appendChild(maskBlock);
        tableMask.appendChild(resultBlock);
        
        prizeGroup.appendChild(prizeTitle);
        prizeGroup.appendChild(tableMask);
        formBody.appendChild(prizeGroup);
    }

    // 為當前獎項創建一個已中獎者清單
    let currentPrizeWinners = new Set();
    
    // 根據設定決定是否使用原始參與者列表
    let currentParticipants;
    if (window.currentSettings.winRule === 'multiple') {
        // 如果允許重複中獎，每次都使用完整的參與者列表
        currentParticipants = [...drawingState.remainingParticipants];
    } else {
        // 如果不允許重複中獎，使用當前剩餘的參與者列表
        currentParticipants = participants;
    }

    async function processWinner(winner) {
        // 取得表格的 tbody
        const tbody = prizeGroup.querySelector('tbody');
        if (!tbody) {
            console.error('找不到表格主體');
            return;
        }
        
        // 建立中獎者表格行
        const tr = document.createElement('tr');
        
        // 根據 excelHeader 建立表格行
        if (window.excelHeader && Array.isArray(window.excelHeader)) {
            // 從 Excel 匯入的資料，winner 是 tab 分隔的字串
            const winnerData = winner.split('\t');
            window.excelHeader.forEach((header, index) => {
                const td = document.createElement('td');
                td.textContent = winnerData[index] || '';
                tr.appendChild(td);
            });
        } else {
            // 簡單資料，只有序號和中獎者
            const indexTd = document.createElement('td');
            indexTd.textContent = tbody.children.length + 1;
            tr.appendChild(indexTd);
            
            const winnerTd = document.createElement('td');
            winnerTd.textContent = winner;
            tr.appendChild(winnerTd);
        }
        
        tbody.appendChild(tr);

        // 儲存中獎紀錄
        const winners = JSON.parse((await idbGetItem('lotteryWinners')) || '{}');
        if (!winners[prizeName]) winners[prizeName] = [];
        winners[prizeName].push(winner);
        await idbSetItem('lotteryWinners', JSON.stringify(winners));
        if (window.currentSettings.winRule !== 'multiple') {
            const winnerIndex = drawingState.remainingParticipants.indexOf(winner);
            if (winnerIndex !== -1) {
                drawingState.remainingParticipants.splice(winnerIndex, 1);
            }
        }
    }

    async function drawOneWinner() {
        // 檢查是否已經抽完所有名額
        if (drawingState.currentCount >= prizeCount) {
            drawingState.currentPrizeIndex++;
            drawingState.currentCount = 0;

            if (window.currentSettings.drawMode === 'sequential') {
                // 檢查是否還有下一個獎項
                if (drawingState.currentPrizeIndex >= prizes.length) {
                    // 已經是最後一個獎項，抽獎完成
                    celebrateCompletion();
                    drawingState.isDrawing = false;
                    setControlButtonsState(true);  // 抽獎結束時重新啟用所有控制項
                    setDrawButtonState(true);      // 重新啟用抽獎按鈕
                    updateDrawButton();
                } else {
                    // 還有下一個獎項，設置為繼續狀態
                    setDrawButtonState(true);  // 只啟用抽獎按鈕
                    setControlButtonsState(false);  // 保持其他控制項禁用
                    updateDrawButton();
                }
            } else {
                setTimeout(() => drawNextPrize(currentParticipants), 500);
            }
            return;
        }

        // 在動畫進行時禁用所有按鈕
        setDrawButtonState(false);
        setControlButtonsState(false);

        // 過濾掉當前獎項已中獎的參與者
        const availableParticipants = currentParticipants.filter(p => !currentPrizeWinners.has(p));

        // 如果還有可用的參與者，進行抽獎
        if (availableParticipants.length > 0) {
            // 隨機抽取一位參與者
            const randomIndex = Math.floor(Math.random() * availableParticipants.length);
            const winner = availableParticipants[randomIndex];

            // 將中獎者加入當前獎項的中獎名單
            currentPrizeWinners.add(winner);

            // 如果不允許重複中獎，從總參與者列表中移除
            if (window.currentSettings.winRule !== 'multiple') {
                const winnerIndex = currentParticipants.indexOf(winner);
                if (winnerIndex !== -1) {
                    currentParticipants.splice(winnerIndex, 1);
                }
            }

            await processWinner(winner);
            drawingState.currentCount++;
            await drawOneWinner();
        } else if (window.currentSettings.winRule !== 'multiple' || availableParticipants.length === 0) {
            // 處理從缺情況
            const remainingCount = prizeCount - drawingState.currentCount;

            // 跳過動畫，直接處理從缺
            const winners = JSON.parse((await idbGetItem('lotteryWinners')) || '{}');
            if (!winners[prizeName]) winners[prizeName] = [];
            
            const tbody = prizeGroup.querySelector('tbody');
            if (!tbody) {
                console.error('找不到表格主體');
                return;
            }
            for (let i = 0; i < remainingCount; i++) {
                const missingText = `(從缺#${i + 1})`;
                
                // 建立從缺表格行
                const tr = document.createElement('tr');
                tr.className = 'missing';
                
                if (window.excelHeader && Array.isArray(window.excelHeader)) {
                    // 從 Excel 匯入的資料，建立對應欄位的從缺行
                    window.excelHeader.forEach((header, index) => {
                        const td = document.createElement('td');
                        if (index === 0) {
                            td.textContent = missingText;
                        } else {
                            td.textContent = '';
                        }
                        tr.appendChild(td);
                    });
                } else {
                    // 簡單資料
                    const indexTd = document.createElement('td');
                    indexTd.textContent = tbody.children.length + 1;
                    tr.appendChild(indexTd);
                    
                    const missingTd = document.createElement('td');
                    missingTd.textContent = missingText;
                    tr.appendChild(missingTd);
                }
                
                tbody.appendChild(tr);
                winners[prizeName].push(missingText);
            }
            await idbSetItem('lotteryWinners', JSON.stringify(winners));
            drawingState.currentCount = prizeCount;
            await drawOneWinner();
        }
    }
    
    drawOneWinner();
}

// 更新抽獎按鈕文字和狀態
function updateDrawButton() {
    const submitButton = document.querySelector('.js-lottery');
    if (!submitButton) return;
    
    // 取得所有獎項列
    const prizes = getPrizeList();
    
    if (window.currentSettings.drawMode === 'sequential' && drawingState.isDrawing) {
        if (drawingState.currentPrizeIndex >= prizes.length) {
            submitButton.textContent = '抽獎';
            drawingState.isDrawing = false;
            setControlButtonsState(true);  // 全部抽完才啟用所有控制項
            setDrawButtonState(true);
        } else {
            submitButton.textContent = '繼續';
            setControlButtonsState(false);  // 保持其他控制項禁用
            setDrawButtonState(true);       // 只啟用繼續按鈕
        }
    } else {
        submitButton.textContent = '抽獎';
        setControlButtonsState(true);  // 非抽獎狀態時啟用所有控制項
        setDrawButtonState(true);
    }
}

// 修改 celebrateCompletion 函數
function celebrateCompletion() {
    // 確保在慶祝動畫後啟用所有按鈕（除了錄影按鈕）
    const allButtons = document.querySelectorAll('button:not(#recordButton)');
    allButtons.forEach(button => button.disabled = false);
    drawingState.isDrawing = false;
    updateDrawButton();
}

// 修改 downloadResults
window.downloadResults = async function(mode) {
    const winners = JSON.parse((await idbGetItem('lotteryWinners')) || '{}');
    const themeElement = document.querySelector('.kv__item');
    const theme = themeElement ? themeElement.textContent.trim() : '抽獎活動';
    const timestamp = new Date().toLocaleString('zh-TW', {
        year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit'
    });
    // 取得 excelHeader
    let excelHeader = undefined;
    try {
        const headerStr = await idbGetItem('excelHeader');
        if (headerStr) {
            excelHeader = JSON.parse(headerStr);
        }
    } catch (e) {}
    // 準備資料陣列
    const data = [];
    data.push([`抽獎主題：${theme}`]);
    data.push([`抽獎時間：${timestamp}`]);
    data.push([]);
    for (const [prizeName, prizeWinners] of Object.entries(winners)) {
        data.push([`${prizeName} (${prizeWinners.length}名)：`]);
        // 標題列
        let headerRow = ["序號", "中獎者"];
        if (excelHeader && Array.isArray(excelHeader) && excelHeader.length > 0) {
            headerRow = ["序號", ...excelHeader];
        }
        data.push(headerRow);
        // 內容列
        const processedWinners = prizeWinners.map((winner, index) => {
            if (mode === 'masked') {
                if (excelHeader && Array.isArray(excelHeader) && excelHeader.length > 0) {
                    // 若有 header，將 tab 分隔的資料分欄並遮罩每一欄
                    const parts = winner.split('\t');
                    const maskedParts = parts.map(part => maskData(part));
                    return [ `${index + 1}.`, ...maskedParts ];
                } else {
                    // 沒有 header 則整行遮罩
                    return [ `${index + 1}.`, maskData(winner) ];
                }
            } else {
                if (excelHeader && Array.isArray(excelHeader) && excelHeader.length > 0) {
                    // 若有 header，將 tab 分隔的資料分欄
                    const parts = winner.split('\t');
                    return [ `${index + 1}.`, ...parts ];
                } else {
                    return [ `${index + 1}.`, winner ];
                }
            }
        });
        data.push(...processedWinners);
        data.push([]);
    }
    // 產生 xlsx
    const ws = XLSX.utils.aoa_to_sheet(data);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, '中獎名單');
    const wbout = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
    const blob = new Blob([wbout], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    const a = document.createElement('a');
    a.href = URL.createObjectURL(blob);
    a.download = `${theme}_中獎名單${mode === 'masked' ? '_遮罩版' : ''}.xlsx`;
    a.click();
    URL.revokeObjectURL(a.href);
};

// 通用的遮罩函數
// 將遮罩邏輯函數化：前後各保留 length/3，中間用指定字元遮罩
function maskMiddle(text, maskChar) {
    const length = text.length;
    if (length <= 2) {
        return text.charAt(0) + maskChar.repeat(length - 1);
    }
    const seg = Math.floor(length / 3);
    const prefix = text.slice(0, seg);
    const suffix = text.slice(-seg);
    const maskLen = length - seg * 2;
    return prefix + maskChar.repeat(maskLen) + suffix;
}

function maskData(text) {
    const lines = text.trim().split('\n');
    
    const maskedLines = lines.map((line) => {
        if (!line.trim()) return line;
        
        // 只用 tab 分割資料
        const parts = line.split('\t');
        
        const maskedParts = parts.map(part => {
            if (!part) return part;

            // 編號開頭的特殊遮罩規則
            const match = part.match(/^(#\d+ )(.*)$/);
            if (match) {
                // match[1] 是 #編號+空格，match[2] 是名字
                const name = match[2];
                let maskedName;
                if (/[\u4e00-\u9fa5]/.test(name)) {
                    maskedName = maskMiddle(name, '◯');
                } else {
                    maskedName = maskMiddle(name, '*');
                }
                return match[1] + maskedName;
            }

            // 電子郵件的遮罩規則
            if (part.includes('@')) {
                const [username, domain] = part.split('@');
                const maskedUsername = maskMiddle(username, '*');
                return `${maskedUsername}@${domain}`;
            }
            
            // 中文名字的遮罩規則
            if (/[\u4e00-\u9fa5]/.test(part)) {
                return maskMiddle(part, '◯');
            }
            
            // 一般文字的遮罩規則
            return maskMiddle(part, '*');
        });

        // 根據需要添加序號
        return maskedParts.join('\t');
    });
    
    return maskedLines.join('\n');
}

// 工具函數：觸發 input 事件
function triggerParticipantsInputEvent() {
    const textarea = document.getElementById('participants');
    if (textarea) {
        const event = new Event('input', { bubbles: true });
        textarea.dispatchEvent(event);
    }
}

// 更新參與者數量顯示
function updateParticipantsCount() {
    const textarea = document.getElementById('participants');
    const countElement = document.getElementById('participants-count');

    if (!textarea || !countElement) return;

    // 計算非空行數
    const lines = textarea.value.split(/\r?\n/).filter(line => line.trim() !== '');
    countElement.textContent = lines.length;
}

// 修改 shuffleParticipants
window.shuffleParticipants = function() {
    const textarea = document.getElementById('participants');
    // 先存進 undo stack
    participantsUndoStack.push({
        value: textarea.value,
        excelHeader: window.excelHeader
    });
    const lines = textarea.value.trim().split('\n');
    for (let i = lines.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [lines[i], lines[j]] = [lines[j], lines[i]];
    }
    textarea.value = lines.join('\n');
    saveToStorage(STORAGE_KEYS.PARTICIPANTS, textarea.value);
    updateParticipantsCount();
    triggerParticipantsInputEvent();
}

// 修改 clearParticipants
window.clearParticipants = function() {
    if (confirm('確定要清除所有待抽名單嗎？')) {
        const textarea = document.getElementById('participants');
        // 清除前先存進 undo stack
        participantsUndoStack.push({
            value: textarea.value,
            excelHeader: window.excelHeader
        });
        textarea.value = '';
        saveToStorage(STORAGE_KEYS.PARTICIPANTS, '');
        window.excelHeader = undefined; // 清除標頭
        idbRemoveItem('excelHeader'); // 刪除 IndexedDB 裡的 excelHeader
        updateParticipantsCount();
        triggerParticipantsInputEvent();
    }
};

// 修改 undoParticipants，直接觸發 textarea 的 undo
window.undoParticipants = function() {
    const textarea = $('#participants')[0];
    if (textarea) {
        if (participantsUndoStack.length > 0) {
            const last = participantsUndoStack.pop();
            textarea.value = last.value;
            window.excelHeader = last.excelHeader;
            if (last.excelHeader) {
                idbSetItem('excelHeader', JSON.stringify(last.excelHeader));
            } else {
                idbRemoveItem('excelHeader');
            }
            saveToStorage(STORAGE_KEYS.PARTICIPANTS, textarea.value);
            
            // 重新渲染表格
            const lines = textarea.value.split(/\r?\n/).filter(line => line.trim() !== '');
            let data = lines.map(line => line.split('\t'));
            if (window.excelHeader && Array.isArray(window.excelHeader) && window.excelHeader.length > 0) {
                data = [window.excelHeader, ...data];
            }
            renderExcelTable(data);
            updateParticipantsCount();
            triggerParticipantsInputEvent();
        } else {
            // 沒有自訂 undo，才用原生
            textarea.focus();
            document.execCommand('undo');
        }
    }
};

// 修改現有的功能函數，加入儲存功能
window.reversePrizes = function() {
    var awardsBlock = $('#awards-block');
    var allRows = awardsBlock.find('.awards__row');
    if (allRows.length <= 1) return;
    var rowsArr = allRows.toArray().reverse();
    awardsBlock.empty();
    rowsArr.forEach(function(row) {
        awardsBlock.append(row);
    });
    // 直接儲存到 IndexedDB，不同步到 textarea
    if (typeof saveAwardsToStorage === 'function') {
        saveAwardsToStorage();
    }
};

window.clearPrizes = function() {
    if (confirm('確定要清除所有獎項內容嗎？')) {
        var awardsBlock = $('#awards-block');
        var allRows = awardsBlock.find('.awards__row');

        // 如果沒有任何獎項列可以當作模板，就直接結束
        if (allRows.length === 0) {
            console.error('找不到獎項模板，無法執行清除。');
            return;
        }

        // 複製第一列作為乾淨的模板，並清空其內容
        var templateRow = allRows.first().clone();
        templateRow.find('input[type="text"]').val('');
        templateRow.find('input[type="number"]').val('1');

        // 移除所有現有的獎項列
        awardsBlock.empty();

        // 附加兩列新的、乾淨的獎項列，還原成初始設計
        awardsBlock.append(templateRow.clone());
        awardsBlock.append(templateRow);

        // 直接儲存到 IndexedDB，不同步到 textarea
        if (typeof saveAwardsToStorage === 'function') {
            saveAwardsToStorage();
        }
    }
};

let mediaRecorder = null;
let recordButton = null;
let recordedChunks = []; // 用來收集所有錄影數據塊
let isRecording = false; // 防止重複操作

async function toggleRecording() {
    if (!isRecording) {
        // 開始錄影
        try {
            isRecording = true;
            recordedChunks = []; // 清空之前的數據
            // 獲取螢幕錄影
            const displayStream = await navigator.mediaDevices.getDisplayMedia({
                video: {
                    displaySurface: "monitor"
                },
                audio: true // 嘗試獲取系統音訊
            });

            let combinedStream = displayStream;

            // 嘗試獲取麥克風音訊（使用更保守的設定）
            try {
                const micStream = await navigator.mediaDevices.getUserMedia({
                    audio: {
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true,
                        sampleRate: 44100,  // 明確指定採樣率
                        channelCount: 2     // 立體聲
                    }
                });

                // 簡化音訊處理，避免複雜的合併可能導致編碼問題
                console.log('嘗試合併音訊...');

                try {
                    // 使用標準採樣率，相容性更好
                    const audioContext = new AudioContext({
                        sampleRate: 44100 // 使用 CD 品質採樣率，相容性最好
                    });
                    const destination = audioContext.createMediaStreamDestination();

                    // 創建增益節點
                    const displayGain = audioContext.createGain();
                    const micGain = audioContext.createGain();

                    // 調整音量
                    displayGain.gain.value = 0.6; // 降低螢幕音訊
                    micGain.gain.value = 0.8;     // 稍微降低麥克風音訊

                    // 添加螢幕音訊
                    if (displayStream.getAudioTracks().length > 0) {
                        const displayAudioSource = audioContext.createMediaStreamSource(displayStream);
                        displayAudioSource.connect(displayGain);
                        displayGain.connect(destination);
                        console.log('已添加螢幕音訊');
                    }

                    // 添加麥克風音訊
                    const micAudioSource = audioContext.createMediaStreamSource(micStream);
                    micAudioSource.connect(micGain);
                    micGain.connect(destination);
                    console.log('已添加麥克風音訊');

                    // 創建合併串流
                    combinedStream = new MediaStream([
                        ...displayStream.getVideoTracks(),
                        ...destination.stream.getAudioTracks()
                    ]);

                    console.log('音訊合併成功');
                } catch (audioError) {
                    console.warn('音訊合併失敗，使用簡單合併:', audioError);
                    // 如果合併失敗，使用簡單的音訊軌道合併
                    const allTracks = [
                        ...displayStream.getVideoTracks(),
                        ...displayStream.getAudioTracks(),
                        ...micStream.getAudioTracks()
                    ];
                    combinedStream = new MediaStream(allTracks);
                }

                console.log('成功合併螢幕錄影和麥克風音訊');
            } catch (micError) {
                console.warn('無法獲取麥克風權限，僅錄製螢幕:', micError);
                // 如果無法獲取麥克風，就只使用螢幕錄影
                combinedStream = displayStream;
            }

            // 檢查最終串流的音訊軌道
            const audioTracks = combinedStream.getAudioTracks();
            const videoTracks = combinedStream.getVideoTracks();
            console.log('最終串流包含:', videoTracks.length, '個視訊軌道,', audioTracks.length, '個音訊軌道');

            if (audioTracks.length > 0) {
                audioTracks.forEach((track, index) => {
                    console.log(`音訊軌道 ${index}:`, track.label, track.getSettings());
                });
            }

            // 專為 Windows Media Player 優化的格式選擇
            let options = {};
            let fileExtension = '.mp4';

            // 測試各種編碼組合，優先選擇 Windows Media Player 支援的格式
            const codecTests = [
                { mimeType: 'video/mp4;codecs=avc1.42E01E,mp4a.40.2', name: 'MP4 + H.264 Baseline + AAC-LC' },
                { mimeType: 'video/mp4;codecs=h264,aac', name: 'MP4 + H.264 + AAC' },
                { mimeType: 'video/webm;codecs=h264,aac', name: 'WebM + H.264 + AAC' },
                { mimeType: 'video/webm;codecs=vp8,pcm', name: 'WebM + VP8 + PCM' },
                { mimeType: 'video/webm;codecs=vp9,pcm', name: 'WebM + VP9 + PCM' },
                { mimeType: 'video/webm', name: 'WebM 預設' }
            ];

            let selectedCodec = null;
            for (const codec of codecTests) {
                if (MediaRecorder.isTypeSupported(codec.mimeType)) {
                    selectedCodec = codec;
                    break;
                }
            }

            if (selectedCodec) {
                options = { mimeType: selectedCodec.mimeType };
                console.log('使用編碼:', selectedCodec.name, '(', selectedCodec.mimeType, ')');

                // 如果是 WebM 但包含 H.264，改用 .mp4 副檔名
                if (selectedCodec.mimeType.includes('webm') && selectedCodec.mimeType.includes('h264')) {
                    fileExtension = '.mp4';
                } else if (selectedCodec.mimeType.includes('mp4')) {
                    fileExtension = '.mp4';
                } else {
                    fileExtension = '.webm';
                }
            } else {
                console.log('使用預設格式');
                options = {};
                fileExtension = '.mp4';
            }

            // 為 Windows Media Player 優化的 MediaRecorder 設定
            const mediaRecorderOptions = {
                ...options,
                videoBitsPerSecond: 2000000, // 2 Mbps，保守的位元率
                audioBitsPerSecond: 96000,   // 96 kbps，較保守的音訊位元率，相容性更好
            };

            // 如果選擇的是 AAC 編碼，添加額外的音訊設定
            if (options.mimeType && options.mimeType.includes('aac')) {
                console.log('使用 AAC 音訊編碼，設定相容參數');
            }

            mediaRecorder = new MediaRecorder(combinedStream, mediaRecorderOptions);

            // 收集錄影數據塊
            mediaRecorder.addEventListener("dataavailable", (evt) => {
                if (evt.data.size > 0) {
                    recordedChunks.push(evt.data);
                    console.log('收集到數據塊，大小:', evt.data.size);
                }
            });

            // 錄影停止時處理所有數據
            mediaRecorder.addEventListener("stop", () => {
                console.log('錄影停止，總共收集到', recordedChunks.length, '個數據塊');

                if (recordedChunks.length > 0) {
                    // 合併所有數據塊
                    const blob = new Blob(recordedChunks, {
                        type: options.mimeType || 'video/mp4'
                    });

                    console.log('合併後的檔案大小:', blob.size);

                    // 下載檔案
                    const a = document.createElement("a");
                    a.href = URL.createObjectURL(blob);
                    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
                    a.download = `抽獎過程_${timestamp}${fileExtension}`;
                    a.click();

                    // 清理資源
                    setTimeout(() => {
                        URL.revokeObjectURL(a.href);
                    }, 1000);
                }

                // 重置狀態
                recordedChunks = [];
                isRecording = false;
                mediaRecorder = null;
            });
            
            // 監聽使用者停止分享螢幕
            const [video] = combinedStream.getVideoTracks();
            video.addEventListener("ended", () => {
                if (isRecording) {
                    stopRecording();
                }
            });

            // 開始錄影（不指定時間間隔，讓它在停止時一次性產生數據）
            mediaRecorder.start();
            
            // 更新按鈕狀態
            window.recordButton.innerHTML = '<span class="icon icon--recording"></span>停止';

        } catch (err) {
            console.error("無法開始錄影:", err);
            alert("無法開始錄影，請確認已授予螢幕錄影權限。");
            isRecording = false;
            recordedChunks = [];
        }
    } else {
        stopRecording();
    }
}

function stopRecording() {
    if (isRecording && mediaRecorder && mediaRecorder.state !== 'inactive') {
        console.log('停止錄影...');
        mediaRecorder.stop();

        // 停止所有軌道
        if (mediaRecorder.stream) {
            mediaRecorder.stream.getTracks().forEach(track => track.stop());
        }

        // 恢復按鈕狀態
        window.recordButton.innerHTML = '<span class="icon icon--recording"></span>錄製';

        // 注意：不在這裡重置 mediaRecorder 和 isRecording，
        // 因為這些會在 "stop" 事件處理器中處理
    }
}

// 添加視窗關閉警告
function setupBeforeUnloadWarning() {
    window.addEventListener('beforeunload', function(e) {
        // 檢查是否正在抽獎中（按鈕被禁用）或是在序列模式下等待繼續
        const submitButton = document.querySelector('#lottery-form button[type="submit"]');
        const isDrawing = submitButton && submitButton.disabled;
        const isWaitingForContinue = window.currentSettings.drawMode === 'sequential' && 
                                    drawingState.isDrawing && 
                                    submitButton.textContent === '繼續';

        if (isDrawing || isWaitingForContinue) {
            // 設定提示訊息
            const message = "抽獎正在進行中，若現在離開可能導致抽獎失效\n你確定要現在離開嗎?";
            e.returnValue = message; // Chrome requires returnValue to be set
            return message; // For other browsers
        }
    });
}

// 在網頁載入時設定警告
document.addEventListener('DOMContentLoaded', async function() {
    // setupBeforeUnloadWarning();
    await loadStoredData();
    setupStorageListeners();
    setupAwardsSwitch(); // 初始化獎項切換功能
    
    // 初始化錄製按鈕
    const recordBtn = document.getElementById('recordButton');
    if (recordBtn) {
        recordBtn.addEventListener('click', toggleRecording);
        // 讓 toggleRecording 裡的 recordButton 指向這個按鈕
        window.recordButton = recordBtn;
    }
    
    // 載入設定
    const savedSettings = await idbGetItem(STORAGE_KEYS.SETTINGS);
    if (savedSettings) {
        window.currentSettings = JSON.parse(savedSettings);
    }
    drawingState = {
        isDrawing: false,
        currentPrizeIndex: 0,
        currentCount: 0,
        remainingParticipants: []
    };
    // 載入 excelHeader 與 participants 並渲染表格
    const headerStr = await idbGetItem('excelHeader');
    window.excelHeader = headerStr ? JSON.parse(headerStr) : undefined;
    const participants = await idbGetItem(STORAGE_KEYS.PARTICIPANTS);
    if (participants) {
        const lines = participants.split(/\r?\n/).filter(line => line.trim() !== '');
        let data = lines.map(line => line.split('\t'));
        if (window.excelHeader && Array.isArray(window.excelHeader) && window.excelHeader.length > 0) {
            data = [window.excelHeader, ...data];
        }
        renderExcelTable(data);
    } else {
        renderExcelTable([]);
    }
    // 檢查是否有中獎紀錄，如果有則顯示結果區域
    const winners = await idbGetItem('lotteryWinners');
    if (winners && JSON.parse(winners) && Object.keys(JSON.parse(winners)).length > 0) {
        const resultSection = document.querySelector('.result');
        if (resultSection) {
            resultSection.style.display = 'block';
        }
        await loadWinnersFromIndexedDB();
    }
});

// 添加 Excel 匯入功能
window.importExcel = async function() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.xlsx, .xls';
    input.onchange = async function(e) {
        const file = e.target.files[0];
        if (!file) return;
        try {
            const data = await readExcelFile(file);
            if (data.length > 1) {
                const lines = data.slice(1).map(row => row.join('\t'));
                const text = lines.join('\n');
                const participantsTextarea = document.getElementById('participants');
                // 匯入前存一筆（要存「匯入前」的 header）
                participantsUndoStack.push({
                    value: participantsTextarea.value,
                    excelHeader: window.excelHeader // 這裡是舊的
                });
                participantsTextarea.value = text;
                await saveToStorage(STORAGE_KEYS.PARTICIPANTS, text);
            }
            if (data.length > 0) {
                window.excelHeader = data[0];
                await idbSetItem('excelHeader', JSON.stringify(data[0]));
            } else {
                window.excelHeader = undefined;
                await idbRemoveItem('excelHeader');
            }
            renderExcelTable(data);
            updateParticipantsCount();

        } catch (error) {
            console.error('匯入Excel時發生錯誤:', error);
            alert('匯入Excel時發生錯誤，請確認檔案格式是否正確。');
        }
    };
    input.click();
};

// 讀取 Excel 檔案內容，回傳二維陣列（每一列為一個 array，第一列為標頭）
async function readExcelFile(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        
        reader.onload = async function(e) {
            try {
                const data = new Uint8Array(e.target.result);
                const workbook = XLSX.read(data, { type: 'array' });
                // 取得第一個工作表
                const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
                // 轉成二維陣列
                let jsonData = XLSX.utils.sheet_to_json(firstSheet, { header: 1 });
				// 消毒：將所有 cell 內容的換行符號都替換成空格
                jsonData = jsonData.map(row =>
                    row.map(cell =>
                        (typeof cell === 'string') ? cell.replace(/[\r\n]+/g, ' ') : cell
                    )
                );
                // 過濾空行
                const filtered = jsonData.filter(row => row && row.some(cell => cell !== null && cell !== undefined && cell.toString().trim() !== ''));
                resolve(filtered);
            } catch (error) {
                reject(error);
            }
        };
        reader.onerror = reject;
        reader.readAsArrayBuffer(file);
    });
}

// 新增：渲染 Excel 表格到 excel-table-container
function renderExcelTable(data) {
    const container = $('#table-container')[0];
    container.innerHTML = '';
    const $table = $('<table>').css({
        width: '100%',
        borderCollapse: 'collapse'
    });
    const table = $table[0];
    if (!data || data.length === 0) {
        const $tbody = $('<span>無匯入資料</span><p>　</p>');
        $table.append($tbody);
        container.appendChild(table);
        return;
    }
    // 只有有 excelHeader 時才渲染 thead
    let startRow = 0;
    if (window.excelHeader && Array.isArray(window.excelHeader) && data.length > 0 &&
        JSON.stringify(data[0]) === JSON.stringify(window.excelHeader)) {
        const $thead = $('<thead>');
        const $headerRow = $('<tr>');
        data[0].forEach(cell => {
            $('<th>').text(cell).appendTo($headerRow);
        });
        $thead.append($headerRow);
        $table.append($thead);
        startRow = 1;
    }
    // 內容
    const $tbody = $('<tbody>');
    for (let i = startRow; i < data.length; i++) {
        const $row = $('<tr>');
        data[i].forEach(cell => {
            $('<td>').text(cell).appendTo($row);
        });
        $tbody.append($row);
    }
    $table.append($tbody);
    container.appendChild(table);
}

// 修改 saveSettings
window.saveSettings = async function() {
    const drawMode = $('#radio-pop-1-1').is(':checked') ? 'all' : 'sequential';
    const skipAnimation = !$('#radio-pop-3-1').is(':checked'); // 修正：開啟動畫時 skipAnimation 應該是 false
    const winRule = $('#radio-pop-2-1').is(':checked') ? 'multiple' : 'single';
    window.currentSettings = { drawMode, skipAnimation, winRule };
    await idbSetItem(STORAGE_KEYS.SETTINGS, JSON.stringify(window.currentSettings));
};

// 新增儲存中獎結果的函數
async function saveWinnersToLocalStorage(prizeName, winner) {
    let winners = JSON.parse((await idbGetItem('lotteryWinners')) || '{}');
    if (!winners[prizeName]) winners[prizeName] = [];
    if (!winners[prizeName].includes(winner)) {
        winners[prizeName].push(winner);
        await idbSetItem('lotteryWinners', JSON.stringify(winners));
    }
}

async function loadWinnersFromIndexedDB() {
    const winners = JSON.parse((await idbGetItem('lotteryWinners')) || '{}');
    const resultSection = document.querySelector('.result');
    if (!resultSection) {
        console.error('找不到結果區塊');
        return;
    }
    const formBody = resultSection.querySelector('.form__body');
    if (!formBody) {
        console.error('找不到表單主體');
        return;
    }
    
    if (formBody) {
        formBody.innerHTML = '';
    }
    
    if (Object.keys(winners).length > 0) {
        if (resultSection) {
            resultSection.style.display = 'block';
        }
    } else {
        if (resultSection) {
            resultSection.style.display = 'none';
        }
    }
    
    for (const [prizeName, prizeWinners] of Object.entries(winners)) {
        const prizeGroup = document.createElement('div');
        prizeGroup.className = 'result__wrap';
        prizeGroup.setAttribute('data-prize', prizeName);
        
        const prizeTitle = document.createElement('div');
        prizeTitle.className = 'result__title';
        prizeTitle.textContent = `${prizeName} ${prizeWinners.length}名`;
        
        const tableMask = document.createElement('div');
        tableMask.className = 'table__mask';
        
        const maskBlock = document.createElement('div');
        maskBlock.className = 'table__mask-block';
        for (let i = 0; i < 4; i++) {
            const maskItem = document.createElement('div');
            maskItem.className = 'table__mask-item';
            maskBlock.appendChild(maskItem);
        }
        
        const resultBlock = document.createElement('div');
        resultBlock.className = 'result__block';
        resultBlock.setAttribute('data-simplebar', '');
        
        const simplebarBlock = document.createElement('div');
        simplebarBlock.className = 'simplebar__block';
        
        const table = document.createElement('table');
        
        const thead = document.createElement('thead');
        const headerRow = document.createElement('tr');
        
        if (window.excelHeader && Array.isArray(window.excelHeader)) {
            window.excelHeader.forEach(header => {
                const th = document.createElement('th');
                th.textContent = header;
                headerRow.appendChild(th);
            });
        } else {
            const defaultHeaders = ['序號', '中獎者'];
            defaultHeaders.forEach(header => {
                const th = document.createElement('th');
                th.textContent = header;
                headerRow.appendChild(th);
            });
        }
        
        thead.appendChild(headerRow);
        table.appendChild(thead);

        // 根據欄位數量設定表格CSS類別
        const columnCount = headerRow.children.length;
        if (columnCount === 2) {
            table.classList.add('cols-2');
        } else if (columnCount === 3) {
            table.classList.add('cols-3');
        } else if (columnCount === 4) {
            table.classList.add('cols-4');
        } else if (columnCount === 5) {
            table.classList.add('cols-5');
        } else if (columnCount >= 6) {
            table.classList.add('cols-many');
        }

        const tbody = document.createElement('tbody');
        table.appendChild(tbody);
        
        simplebarBlock.appendChild(table);
        resultBlock.appendChild(simplebarBlock);
        tableMask.appendChild(maskBlock);
        tableMask.appendChild(resultBlock);
        
        prizeGroup.appendChild(prizeTitle);
        prizeGroup.appendChild(tableMask);
        formBody.appendChild(prizeGroup);
        
        // 載入中獎者資料到表格
        for (const winner of prizeWinners) {
            const tr = document.createElement('tr');
            if (winner.startsWith('(從缺')) {
                tr.className = 'missing';
            }
            
            if (window.excelHeader && Array.isArray(window.excelHeader)) {
                const winnerData = winner.split('\t');
                window.excelHeader.forEach((header, index) => {
                    const td = document.createElement('td');
                    if (winner.startsWith('(從缺')) {
                        td.textContent = index === 0 ? winner : '';
                    } else {
                        td.textContent = winnerData[index] || '';
                    }
                    tr.appendChild(td);
                });
            } else {
                const indexTd = document.createElement('td');
                indexTd.textContent = tbody.children.length + 1;
                tr.appendChild(indexTd);
                
                const winnerTd = document.createElement('td');
                winnerTd.textContent = winner;
                tr.appendChild(winnerTd);
            }
            tbody.appendChild(tr);
        }
    }
}
window.clearWinners = async function() {
    if (confirm('確定要清除所有中獎結果嗎？')) {
        await idbRemoveItem('lotteryWinners');
        
        const resultSection = document.querySelector('.result');
        if (!resultSection) {
            console.error('找不到結果區塊');
            return;
        }
        const formBody = resultSection.querySelector('.form__body');
        if (formBody) {
            formBody.innerHTML = '';
        }
        
        if (resultSection) {
            resultSection.style.display = 'none';
        }
    }
};

// 監聽 participants textarea 內容變動，自動同步渲染表格
(function setupParticipantsSyncTable() {
    const textarea = $('#participants')[0];
    if (!textarea) return;
    textarea.addEventListener('input', function() {
        // 以換行分割每一行
        const lines = textarea.value.split(/\r?\n/).filter(line => line.trim() !== '');
        let data = lines.map(line => line.split('\t'));
        // 僅當有 excelHeader（即從 Excel 匯入過）時才加標頭
        if (window.excelHeader && Array.isArray(window.excelHeader) && window.excelHeader.length > 0) {
            data = [window.excelHeader, ...data];
        }
        renderExcelTable(data);
        updateParticipantsCount();
    });
})();

// 獎項切換顯示功能
function setupAwardsSwitch() {
    const switchBtn = document.querySelector('.js-awards-switch');
    const awardsWraps = document.querySelectorAll('.awards .awards__wrap');
    
    if (!switchBtn || awardsWraps.length < 2) return;
    
    switchBtn.addEventListener('click', function() {
        // 切換顯示狀態
        awardsWraps.forEach(wrap => wrap.classList.toggle('active'));
        
        // 同步資料
        syncAwardsData();
    });
    
    // 監聽獎項輸入框的變動，自動同步到 textarea
    setupAwardsInputListeners();
}

// 設置獎項輸入框監聽
function setupAwardsInputListeners() {
    const awardsBlock = document.getElementById('awards-block');
    const awardsTextarea = document.getElementById('awards-textarea');
    if (!awardsBlock) return;
    // 只監聽 textarea 的變動，表格模式不自動同步
    if (awardsTextarea) {
        const textareaHandler = function() {
            // 只有在表格模式 active 時才同步到表格
            const isTableMode = !awardsTextarea.closest('.awards__wrap').classList.contains('active');
            if (isTableMode) {
                clearTimeout(window.awardsTextareaSyncTimeout);
                window.awardsTextareaSyncTimeout = setTimeout(() => {
                    syncAwardsData();
                }, 300);
            }
        };
        awardsTextarea._inputHandler = textareaHandler;
        awardsTextarea.addEventListener('input', textareaHandler);
    }
}

// 同步獎項資料（只在切換時呼叫）
function syncAwardsData() {
    const awardsBlock = document.getElementById('awards-block');
    const awardsTextarea = document.getElementById('awards-textarea');
    if (!awardsBlock || !awardsTextarea) return;
    if (window.isSyncingAwards) return;
    window.isSyncingAwards = true;
    const isTextareaMode = awardsTextarea.closest('.awards__wrap').classList.contains('active');
    if (isTextareaMode) {
        // 從表格模式同步到 textarea 模式
        const rows = awardsBlock.querySelectorAll('.awards__row');
        const lines = [];
        rows.forEach(row => {
            const nameInput = row.querySelector('input[type="text"]');
            const countInput = row.querySelector('input[type="number"]');
            if (nameInput && countInput) {
                const name = nameInput.value.trim();
                const count = countInput.value.trim();
                if (name) {
                    lines.push(`${name},${count || '1'}`);
                }
            }
        });
        awardsTextarea.value = lines.join('\n');
    } else {
        // 從 textarea 模式同步到表格模式
        const lines = awardsTextarea.value.trim().split('\n').filter(line => line.trim());
        const rows = [];
        lines.forEach(line => {
            const parts = line.split(',');
            const name = parts[0]?.trim() || '';
            const count = parts[1]?.trim() || '1';
            if (name) {
                rows.push({ name, count });
            }
        });
        updateAwardsTable(rows);
    }
    setTimeout(() => {
        window.isSyncingAwards = false;
    }, 100);
}

// 更新獎項表格
function updateAwardsTable(rows) {
    const awardsBlock = document.getElementById('awards-block');
    if (!awardsBlock) {
        console.error('找不到 awards-block 元素');
        return;
    }
    
    awardsBlock.innerHTML = '';
    if (rows.length === 0) {
        rows = [{ name: '', count: '1' }, { name: '', count: '1' }];
    }
    
    rows.forEach((row, index) => {
        const rowDiv = document.createElement('div');
        rowDiv.className = 'awards__row';
        rowDiv.innerHTML = `
            <div class="awards__col">
                <div class="form__item">
                    <input type="text" class="form__input" placeholder="請輸入獎品名稱" value="${row.name}">
                </div>
            </div>
            <div class="awards__col">
                <div class="form__item">
                    <input type="number" class="form__input" placeholder="數量" min="1" value="${row.count}">
                </div>
            </div>
        `;
        awardsBlock.appendChild(rowDiv);
    });
}