// /*-- result --*/
.result
  $root: &
  margin-top: 75px
  display: none
  &.active
    display: block
  .form__body
    margin-top: 55px
    @media (max-width:$bk-tb)
      margin-top: 30px
  &__wrap
    margin-top: 30px
    &:first-child
      margin-top: 0
    &-item
      position: relative
  &__title
    font-size: $fontsize-20
    font-weight: $fontweight-medium
    color: $color-black
    letter-spacing: 0.05em
    position: relative
    padding-left: 44px
    margin-bottom: 20px
    @media (max-width:$bk-sm)
      padding-left: 20px
      font-size: $fontsize-18
    &::before
      content: ""
      display: block
      width: 8px
      height: 8px
      background: $color-orange
      border-radius: 10em
      position: absolute
      top: 10px
      left: 16px
      @media (max-width:$bk-sm)
        top: 8px
        left: 0
        width: 6px
        height: 6px
  &__block
    overflow: auto
    max-height: 576px
    width: 100%
  table
    width: auto
    table-layout: auto
    min-width: 100%
  thead
    tr
      background: $color-orange2
    th
      color: $color-white
      font-size: $fontsize-18
      font-weight: $fontweight-bold
      color: $color-white
      border: 3px solid #ffffff
      border-top: none
      // &:first-child
      //   border-top-left-radius: 12px
      //   overflow: hidden
      // &:last-child
      //   border-top-right-radius: 12px
      //   overflow: hidden
  tbody
    tr
      background: #e6e6e6
      &:last-child
        // td
        //   &:first-child
        //     border-bottom-left-radius: 12px
        //     overflow: hidden
        //   &:last-child
        //     border-bottom-right-radius: 12px
        //     overflow: hidden
    td
      border: 3px solid #ffffff
  th,td
    padding: 14px
    white-space: nowrap
    text-align: center
    vertical-align: middle
    &:first-child
      border-left: none

  // 根據欄位數量調整寬度的類別，使用 min-width 和 calc 確保適應內容
  &.cols-2
    th, td
      min-width: calc(50% - 20px)
      width: auto
      max-width: none

  &.cols-3
    th, td
      min-width: calc(33.33% - 20px)
      width: auto
      max-width: none

  &.cols-4
    th, td
      min-width: calc(25% - 20px)
      width: auto
      max-width: none

  &.cols-5
    th, td
      min-width: calc(20% - 20px)
      width: auto
      max-width: none

  &.cols-many
    th, td
      min-width: 120px
      width: auto
      max-width: none

  // 預設情況（沒有 cols-* 類別時）
  &:not([class*="cols-"])
    th, td
      min-width: 150px
      width: auto
      max-width: none

  // 針對長內容的特殊處理
  th, td
    // 確保文字不會被截斷
    overflow: visible
    text-overflow: clip
    // 如果內容真的太長，可以考慮換行（但通常我們希望保持 nowrap）
    // word-break: break-all

    // 針對第一欄（通常是序號）設定較小的最小寬度
    &:first-child
      min-width: 80px

    // 針對可能包含長文字的欄位，設定更大的最小寬度
    &:nth-child(2)
      min-width: 200px

  // 自適應寬度的特殊處理
  &.adaptive-width
    // 確保表格能夠根據內容擴展
    width: auto
    min-width: 100%

    th, td
      // 讓欄位根據內容自動調整寬度
      width: auto
      // 但保持合理的最小寬度
      min-width: 120px
      // 移除最大寬度限制
      max-width: none

      // 第一欄（序號）保持較小
      &:first-child
        min-width: 60px
        max-width: 100px

      // 針對可能包含長內容的特定欄位
      &.long-content
        min-width: 300px
        max-width: 500px
      // @media (max-width:$bk-xl)
      //   min-width: 350px
      // @media (max-width:$bk-lg)
      //   min-width: 290px
      // @media (max-width:$bk-md)
      //   min-width: 208px
      // @media (max-width:$bk-mblg)
      //   min-width: 130px
    // &:nth-child(2)
    //   min-width: 514px
    //   @media (max-width:$bk-xl)
    //     min-width: 350px
    //   @media (max-width:$bk-lg)
    //     min-width: 290px
    //   @media (max-width:$bk-md)
    //     min-width: 198px
    //   @media (max-width:$bk-mblg)
    //     min-width: 150px
    // &:nth-child(3)
    //   min-width: 700px
    //   @media (max-width:$bk-xl)
    //     min-width: 540px
    //   @media (max-width:$bk-lg)
    //     min-width: 416px
    //   @media (max-width:$bk-md)
    //     min-width: 304px
    //   @media (max-width:$bk-mblg)
    //     min-width: 280px
    
    